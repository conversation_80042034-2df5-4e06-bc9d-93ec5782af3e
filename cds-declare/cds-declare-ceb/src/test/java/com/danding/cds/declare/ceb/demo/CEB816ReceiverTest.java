package com.danding.cds.declare.ceb.demo;

import com.danding.cds.declare.ceb.domain.ceb816.CEB816Message;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import org.junit.Test;

public class CEB816ReceiverTest {

    @Test
    public void run() {
        String requestXmlString = "" +
                "<CEB816Message\n" +
                "    xmlns=\"http://www.chinaport.gov.cn/ceb\" version=\"1.0\" guid=\"ec43d7c9-21bc-448d-8b11-aba3ca9beb5b\">\n" +
                "    <Tax>\n" +
                "        <TaxHeadRd>\n" +
                "            <guid>ec43d7c9-21bc-448d-8b11-aba3ca9beb5b</guid>\n" +
                "            <returnTime>20200709112606787</returnTime>\n" +
                "            <invtNo>29242020I515686762</invtNo>\n" +
                "            <taxNo>29242020I515686762_0</taxNo>\n" +
                "            <customsTax>0.0</customsTax>\n" +
                "            <valueAddedTax>76.58</valueAddedTax>\n" +
                "            <consumptionTax>0.0</consumptionTax>\n" +
                "            <status>1</status>\n" +
                "            <entDutyNo></entDutyNo>\n" +
                "            <note></note>\n" +
                "            <assureCode>330766K00W</assureCode>\n" +
                "            <ebcCode>3301964J31</ebcCode>\n" +
                "            <logisticsCode>3122480063</logisticsCode>\n" +
                "            <agentCode>330766K00W</agentCode>\n" +
                "            <customsCode>2924</customsCode>\n" +
                "            <orderNo>OC22007071300121260S0001</orderNo>\n" +
                "            <logisticsNo>77120706979289</logisticsNo>\n" +
                "        </TaxHeadRd>\n" +
                "        <TaxListRd>\n" +
                "            <gnum>1</gnum>\n" +
                "            <gcode>1901101000</gcode>\n" +
                "            <taxPrice>841.5</taxPrice>\n" +
                "            <customsTax>0.0</customsTax>\n" +
                "            <valueAddedTax>76.58</valueAddedTax>\n" +
                "            <consumptionTax>0.0</consumptionTax>\n" +
                "        </TaxListRd>\n" +
                "    </Tax>\n" +
                "</CEB816Message>";
        try {
            requestXmlString = requestXmlString.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
            CEB816Message response = XMLUtil.converyToJavaBean(requestXmlString, CEB816Message.class);
            System.out.print(response.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
