package com.danding.cds.declare.ceb.internal.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yousx
 * @Date: 2024/07/03
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum CebMessageV2Enum {

    CEB_ORDER("CEB31100", "订单申报", CebMessageEnum.CEB_ORDER),
    CEB_SHIPMENT("CEB51100", "运单申报", CebMessageEnum.CEB_SHIPMENT),
    CEB_INVENTORY("CEB62100", "清单申报", CebMessageEnum.CEB_INVENTORY),
    CEB_INVENTORY_REFUND("CEB62500", "清单退货", CebMessageEnum.CEB_INVENTORY_REFUND),
    CEB_INVENTORY_CANCEL("CEB62300", "清单撤单", CebMessageEnum.CEB_INVENTORY_CANCEL),
    ;
    private final String code;

    private final String desc;

    private final CebMessageEnum cebMessageEnum;

    public static CebMessageV2Enum getEnumByCode(String code) {
        for (CebMessageV2Enum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static CebMessageV2Enum getByCebEnum(CebMessageEnum cebMessageEnum) {
        for (CebMessageV2Enum value : values()) {
            if (value.cebMessageEnum.equals(cebMessageEnum)) {
                return value;
            }
        }
        return null;
    }
}
