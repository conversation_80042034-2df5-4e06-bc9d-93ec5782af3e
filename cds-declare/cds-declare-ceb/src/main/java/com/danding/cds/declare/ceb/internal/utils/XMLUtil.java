package com.danding.cds.declare.ceb.internal.utils;

import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class XMLUtil {

    private static final String element = "[a-zA-Z0-9]+";

    private static final Pattern SEND_ID_PATTERN = Pattern.compile("<SenderId>[a-zA-Z0-9]+</SenderId>");
    private static final Pattern STA_MSG_PATTERN = Pattern.compile("<" + element + ">");
    private static final Pattern ENT_MSG_PATTERN = Pattern.compile("</" + element + ">");
    private static final Pattern HEAD_MSG_PATTERN = Pattern.compile("<" + element + " ");
    private static final Pattern ELEMENT_PATTERN = Pattern.compile(element);


    /**
     * 替换dxp-xml元素sendId
     *
     * @param dxpXmlMsg 数据
     * @param dxpId
     * @return
     */
    public static String replaceDxpXmlSendId(String dxpXmlMsg, String dxpId) {

        if (StringUtils.isEmpty(dxpXmlMsg) || StringUtils.isEmpty(dxpId)) {
            return dxpXmlMsg;
        }
        String replaceStr = "<SenderId>" + dxpId + "</SenderId>";
        Matcher matcher = SEND_ID_PATTERN.matcher(dxpXmlMsg);
        if (matcher.find()) {
            String sendStr = matcher.group();
            return dxpXmlMsg.replace(sendStr, replaceStr);
        }
        return dxpXmlMsg;
    }

    /**
     * 给xml元素添加统一前缀
     *
     * @param xml    数据
     * @param prefix 前缀
     * @return
     */
    public static String xmlElementAddPrefix(String xml, String prefix) {

        if (StringUtils.isEmpty(xml) || StringUtils.isEmpty(prefix)) {
            return xml;
        }
        String resultXml = xml;
        resultXml = xmlElementAddPrefix(resultXml, prefix, HEAD_MSG_PATTERN);
        resultXml = xmlElementAddPrefix(resultXml, prefix, STA_MSG_PATTERN);
        resultXml = xmlElementAddPrefix(resultXml, prefix, ENT_MSG_PATTERN);
        return resultXml;
    }

    private static String xmlElementAddPrefix(String orginXml, String prefix, Pattern pattern) {

        String resultXml = orginXml;
        Matcher matcher = pattern.matcher(resultXml);
        while (matcher.find()) {
            String label = matcher.group();
            Matcher elementMatcher = ELEMENT_PATTERN.matcher(label);
            while (elementMatcher.find()) {
                String element = elementMatcher.group();
                String targetLabel = label.replace(element, prefix + element);
                resultXml = resultXml.replace(label, targetLabel);
            }
        }
        return resultXml;
    }

    private static String getXml(String newXml, String prefix, Pattern pattern, Matcher tagMatcher, String s, String s2) {
        while (tagMatcher.find()) {
            String sendStr = tagMatcher.group();
            Matcher matcher = pattern.matcher(sendStr);
            String _element = "";
            while (matcher.find()) {
                _element = matcher.group();
                newXml = newXml.replace(sendStr, s + prefix + _element + s2);
            }
        }
        return newXml;
    }

    /**
     * JavaBean转换成xml
     * 默认编码UTF-8
     *
     * @param obj
     * @return
     */
    public static String convertToXml(Object obj) throws Exception {
        return convertToXml(obj, "UTF-8");
    }

    /**
     * JavaBean转换成xml
     * 默认编码UTF-8  不带报文头
     */
    public static String convertToXmlWithOutXmlHead(Object obj) throws Exception {
        return convertToXmlWithOutXmlHead(obj, "UTF-8");
    }

    /**
     * JavaBean转换成xml
     *
     * @param obj
     * @param encoding
     * @return
     */
    public static String convertToXml(Object obj, String encoding) throws Exception {
        String result = null;
        if (obj == null) {
            throw new Exception("转换数据对象参数不能为空");
        }
        JAXBContext context = JAXBContext.newInstance(obj.getClass());
        Marshaller marshaller = context.createMarshaller();
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, false);
        marshaller.setProperty(Marshaller.JAXB_ENCODING, encoding);
        marshaller.setProperty(Marshaller.JAXB_FRAGMENT, Boolean.FALSE);
        StringWriter writer = new StringWriter();
        marshaller.marshal(obj, writer);
        result = writer.toString();
        return result;
    }

    /**
     * JavaBean转换成xml
     *
     * @param obj
     * @param encoding
     * @return
     */
    public static String convertToXmlWithOutXmlHead(Object obj, String encoding) throws Exception {
        String result = null;
        if (obj == null) {
            throw new Exception("转换数据对象参数不能为空");
        }
        JAXBContext context = JAXBContext.newInstance(obj.getClass());
        Marshaller marshaller = context.createMarshaller();
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, false);
        marshaller.setProperty(Marshaller.JAXB_ENCODING, encoding);
        marshaller.setProperty(Marshaller.JAXB_FRAGMENT, true);
        StringWriter writer = new StringWriter();
        marshaller.marshal(obj, writer);
        result = writer.toString();
        return result;
    }

    /**
     * xml转换成JavaBean
     *
     * @param xml
     * @param c
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T converyToJavaBean(String xml, Class<T> c) throws Exception {
        T t = null;
        if (StringUtils.isBlank(xml)) {
            return t;
        }
        xml = xml.trim();
        JAXBContext context = JAXBContext.newInstance(c);
        Unmarshaller unmarshaller = context.createUnmarshaller();
        t = (T) unmarshaller.unmarshal(new StringReader(xml));
        return t;
    }

    /**
     * JavaBean转换成xml
     * 默认编码UTF-8
     *
     * @param obj
     * @return
     */
    public static String convertToXmlByDM(Object obj) throws Exception {
        return convertToXmlByDM(obj, "UTF-8");
    }

    /**
     * JavaBean转换成xml
     *
     * @param obj
     * @param encoding
     * @return
     */
    public static String convertToXmlByDM(Object obj, String encoding) throws Exception {
        String result = null;
        JAXBContext context = JAXBContext.newInstance(obj.getClass());
        Marshaller marshaller = context.createMarshaller();
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, false);
        marshaller.setProperty(Marshaller.JAXB_ENCODING, encoding);
        marshaller.setProperty(Marshaller.JAXB_FRAGMENT, Boolean.FALSE);
        StringWriter writer = new StringWriter();
        marshaller.marshal(obj, writer);
        result = writer.toString();
        return result;
    }

    /**
     * 转义XML中的特殊字符
     *
     * @param str 转义前字符
     * @return 转义后字符
     */
    public static String escapeSpecialCharacterForXML(String str) {
        if (str == null) {
            return "";
        }
        str = replaceString(str, "&", "&amp;");
        str = replaceString(str, "<", "&lt;");
        str = replaceString(str, ">", "&gt;");
        str = replaceString(str, "&apos;", "&apos;");
        str = replaceString(str, "\"", "&quot;");
        return str;
    }

    /**
     * 替换一个字符串中的某些指定字符
     *
     * @param str         String 原始字符串
     * @param regex       String 要替换的字符串
     * @param replacement String 替代字符串
     * @return String 替换后的字符串
     */
    private static String replaceString(String str, String regex, String replacement) {
        if (str == null) {
            return null;
        }
        try {
            int index;
            index = str.indexOf(regex);
            String strNew = "";
            if (index >= 0) {
                while (index >= 0) {
                    strNew += str.substring(0, index) + replacement;
                    str = str.substring(index + regex.length());
                    index = str.indexOf(regex);
                }
                strNew += str;
                return strNew;
            }
        } catch (Exception e) {
            return str;
        }
        return str;
    }

    public static void main(String[] args) {

        // 测试特殊字符转义
        String str = "1:& 2: < 3: > 4: ' 5: \"";
//        System.out.println(escapeSpecialCharacterForXML(str));
        String msg = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><DxpMsg ver=\"1.0\"><TransInfo><CopMsgId>8ddbd382-b90a-4cec-a3f6-c9efcd09b650</CopMsgId><SenderId>DXPENT0000471915</SenderId><ReceiverIds><ReceiverId>DXPEDCCEB0000002</ReceiverId></ReceiverIds><CreatTime>2022-04-21T06:00:02.877+08:00</CreatTime><MsgType>CEB311Message</MsgType></TransInfo><Data>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</Data></DxpMsg>";


        String xml = xmlElementAddPrefix(msg, "dxp:");
        System.out.println(xml);
    }
}
