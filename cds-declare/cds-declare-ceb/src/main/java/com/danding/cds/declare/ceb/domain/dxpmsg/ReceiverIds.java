package com.danding.cds.declare.ceb.domain.dxpmsg;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

/**
 * <AUTHOR> by xp
 * @version v1.0
 * @description com.sfebiz.logistics.protocol.ceb.DxpMsg
 * @ClassName ReceiverIds
 * @date 2019/11/4 15:19
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class ReceiverIds implements Serializable {

    @XmlElement(name = "ReceiverId")
    private String receiverId;
}
