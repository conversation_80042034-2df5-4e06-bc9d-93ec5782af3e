package com.danding.cds.declare.ceb.internal.enums;

/**
 * @program: cds-center
 * @description: ceb<PERSON>sage
 * @author: 潘本乐（Belep）
 * @create: 2021-09-25 16:36
 **/
public enum CebMessageEnum {

    CEB_ORDER("CEB311Message", "订单申报"),
    CEB_SHIPMENT("CEB511Message", "运单申报"),
    CEB_INVENTORY_REFUND("CEB625Message", "清单退货"),
    CEB_INVENTORY_CANCEL("CEB623Message", "清单取消"),
    CEB_INVENTORY("CEB621Message", "清单申报"),
    CEB_ORDER_CALLBACK("CEB312Message", "订单回执"),
    CEB_SHIPMENT_CALLBACK("CEB512Message", "运单回执"),
    CEB_INVENTORY_CALLBACK("CEB622Message", "清单回执"),
    CEB_INVENTORY_CANCEL_CALLBACK("CEB624Message", "清单取消回执"),
    CEB_INVENTORY_REFUND_CALLBACK("CEB626Message", "清单退货回执"),
    CEB_TAX_CALLBACK("CEB816Message", "税金回执"),
    CEB_TAX_STATUS_CALLBACK("CEB818Message", "税金状态回执");

    private String tag;

    private String desc;

    CebMessageEnum(String tag, String desc) {
        this.tag = tag;
        this.desc = desc;
    }

    public String getTag() {
        return tag;
    }

    public String getDesc() {
        return desc;
    }

    public static CebMessageEnum getByTag(String tag) {
        for (CebMessageEnum cebMessageEnum : CebMessageEnum.values()) {
            if (cebMessageEnum.getTag().equals(tag)) {
                return cebMessageEnum;
            }
        }
        return null;
    }
}
