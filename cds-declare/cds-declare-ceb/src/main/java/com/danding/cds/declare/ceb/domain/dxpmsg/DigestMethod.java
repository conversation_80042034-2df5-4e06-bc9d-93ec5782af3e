package com.danding.cds.declare.ceb.domain.dxpmsg;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import java.io.Serializable;

/**
 * <AUTHOR> by xp
 * @version v1.0
 * @description com.sfebiz.logistics.protocol.ceb.DxpMsg
 * @ClassName DigestMethod
 * @date 2019/11/4 16:17
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class DigestMethod implements Serializable {

    private static final long serialVersionUID = -5270884460095071841L;
    @XmlAttribute(name = "ds:Algorithm")
    private String algorithm;
}
