package com.danding.cds.declare.ceb.internal.enums;

/**
 * <p>申报类型</p>
 * User: <a href="mailto:<EMAIL>">严明明</a>
 * Date: 15/1/12
 * Time: 下午6:03
 */
public enum CebDeclareType {
    CREATE("1", "新增"),
    EDIT("2", "修改"),
    DELETE("3", "删除");

    private String type;

    private String description;

    CebDeclareType(String type, String description) {
        this.type = type;
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }


    @Override
    public String toString() {
        return type;
    }
}
