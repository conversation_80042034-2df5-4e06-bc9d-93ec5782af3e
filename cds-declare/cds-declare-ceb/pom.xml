<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cds-declare</artifactId>
        <groupId>com.danding</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cds-declare-ceb</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <!--加密机：三未信安[因为加密机是总署通用，所以放在总署的工具里]-->
        <dependency>
            <groupId>com.swxajce</groupId>
            <artifactId>SwxaJCE</artifactId>
            <version>2.0.1-RELEASE</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.swxajce</groupId>-->
<!--            <artifactId>jeromq-0.4.2</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.swxajce</groupId>
            <artifactId>crypto</artifactId>
            <version>2.0.1-RELEASE</version>
        </dependency>
    </dependencies>
</project>