package com.danding.cds.record.api.dto.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-07-19 16:02
 */
@Data
public class EndangeredFactorEnabledParam implements Serializable {
    @NotNull(message = "id不能为空")
    private Long id;

    @NotNull(message = "状态值不能为空")
    private Integer status;

    /**
     * 多个id 用','隔开
     */
    private String ids;
}
