package com.danding.cds.record.api.dto.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-07-27 15:53
 */
@Data
public class EndangeredFactorCheckParam implements Serializable {
    @ApiModelProperty(value = "条码")
    private String barCode;

    @ApiModelProperty(value = "商品备案名")
    private String goodsRecordName;

    @ApiModelProperty(value = "成分")
    private String composition;

    @ApiModelProperty(value = "申报要素")
    private String hgsbys;

    @ApiModelProperty(value = "是否快速失败 默认：否")
    private Boolean needFailFast;
}
